import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import Layout from './components/Layout/Layout';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <Routes>
            {/* Public routes */}
            <Route path="/login" element={<Login />} />

            {/* Protected routes */}
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <Layout />
                </ProtectedRoute>
              }
            >
              <Route index element={<Dashboard />} />
              <Route path="bookings" element={<div className="p-6"><h1 className="text-2xl font-bold">Bookings</h1><p>Bookings management coming soon...</p></div>} />
              <Route path="drivers" element={<div className="p-6"><h1 className="text-2xl font-bold">Drivers</h1><p>Driver management coming soon...</p></div>} />
              <Route path="hubs" element={<div className="p-6"><h1 className="text-2xl font-bold">Hubs</h1><p>Hub management coming soon...</p></div>} />
              <Route path="users" element={<div className="p-6"><h1 className="text-2xl font-bold">Users</h1><p>User management coming soon...</p></div>} />
              <Route path="scanning" element={<div className="p-6"><h1 className="text-2xl font-bold">Scanning</h1><p>Scanning interface coming soon...</p></div>} />
              <Route path="analytics" element={<div className="p-6"><h1 className="text-2xl font-bold">Analytics</h1><p>Analytics dashboard coming soon...</p></div>} />
              <Route path="settings" element={<div className="p-6"><h1 className="text-2xl font-bold">Settings</h1><p>Settings panel coming soon...</p></div>} />
            </Route>
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
