/**
 * @license lucide-react v0.516.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M19 13v6h-6", key: "1hxl6d" }],
  ["path", { d: "M5 11V5h6", key: "12e2xe" }],
  ["path", { d: "m5 5 14 14", key: "11anup" }]
];
const MoveDiagonal2 = createLucideIcon("move-diagonal-2", __iconNode);

export { __iconNode, MoveDiagonal2 as default };
//# sourceMappingURL=move-diagonal-2.js.map
