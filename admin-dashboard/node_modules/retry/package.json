{"author": "<PERSON> <<EMAIL>> (http://debuggable.com/)", "name": "retry", "description": "Abstraction for exponential and custom retry strategies for failed operations.", "license": "MIT", "version": "0.13.1", "homepage": "https://github.com/tim-kos/node-retry", "repository": {"type": "git", "url": "git://github.com/tim-kos/node-retry.git"}, "files": ["lib", "example"], "directories": {"lib": "./lib"}, "main": "index.js", "engines": {"node": ">= 4"}, "dependencies": {}, "devDependencies": {"fake": "0.2.0", "istanbul": "^0.4.5", "tape": "^4.8.0"}, "scripts": {"test": "./node_modules/.bin/istanbul cover ./node_modules/tape/bin/tape ./test/integration/*.js", "release:major": "env SEMANTIC=major npm run release", "release:minor": "env SEMANTIC=minor npm run release", "release:patch": "env SEMANTIC=patch npm run release", "release": "npm version ${SEMANTIC:-patch} -m \"Release %s\" && git push && git push --tags && npm publish"}}