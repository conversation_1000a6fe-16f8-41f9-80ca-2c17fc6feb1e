# Database Configuration (H2 for development)
spring.datasource.url=jdbc:h2:mem:dwelzy_db
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# H2 Console Configuration
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA Configuration
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.properties.hibernate.format_sql=true

# Application Configuration
spring.application.name=dwelzy
server.port=8080

# Security Configuration
spring.security.user.name=admin
spring.security.user.password=admin123
spring.security.user.roles=ADMIN

# Logging Configuration
logging.level.com.dwelzy=DEBUG
logging.level.org.springframework.security=DEBUG
